================================================================================
                    OWNER YETKİSİ İLE KAPSAMLI SPOR SALONU YÖNETİM SİSTEMİ PLANI
================================================================================

PROJE: GymKod Pro - Spor Salonu Yönetim Sistemi
TARİH: 2025-06-24
HAZIRLAYAN: Sistem Analizi ve Planlama Ekibi

================================================================================
1. ANA DASHBOARD (Owner Kontrol Me<PERSON>ezi)
================================================================================

1.1 GENEL BAKIŞ KARTLARI:
-------------------------
• Toplam Spor Salonu Sayısı
  - Aktif salon sayısı
  - Pasif salon sayısı
  - Toplam salon sayısı
  - Yeni eklenen salonlar (son 30 gün)

• Toplam Lisanslı Kullanıcı Sayısı
  - Aktif lisans sahipleri
  - Admin lisansları
  - Member lisansları
  - Yakında bitecek lisanslar

• Aylık Toplam Gelir
  - Bu ay toplam gelir
  - Geçen ay karşılaştırması
  - Yıllık toplam gelir
  - Ortalama salon başı gelir

• Yakında Bitecek Lisanslar
  - 7 gün içinde bitenler
  - 30 gün içinde bitenler
  - Kritik durumda olanlar
  - Yenilenmesi gerekenler

• Sistem Performansı
  - Cache hit oranı
  - API yanıt süreleri
  - Aktif kullanıcı sayısı
  - Sistem yükü

1.2 GRAFİK VE RAPORLAR:
-----------------------
• Salon Bazlı Gelir Karşılaştırması (Bar Chart)
  - Aylık gelir karşılaştırması
  - En karlı salonlar
  - Düşük performanslı salonlar

• Lisans Türü Dağılımı (Pie Chart)
  - Admin lisansları yüzdesi
  - Member lisansları yüzdesi
  - Aktif/Pasif dağılımı

• Aylık Büyüme Trendi (Line Chart)
  - Son 12 ay gelir trendi
  - Salon sayısı artışı
  - Kullanıcı sayısı artışı

• Coğrafi Dağılım
  - Şehir bazlı salon haritası
  - Bölgesel performans analizi
  - Genişleme fırsatları

================================================================================
2. SPOR SALONU YÖNETİMİ
================================================================================

2.1 SALON LİSTESİ VE YÖNETİMİ:
-------------------------------
• Tüm Salonları Görüntüle
  - Sayfalama ile salon listesi
  - Gelişmiş filtreleme seçenekleri
  - Arama fonksiyonu (isim, şehir, telefon)
  - Sıralama seçenekleri (tarih, isim, durum)

• Salon Detayları
  - Şirket bilgileri
  - Adres ve iletişim bilgileri
  - Salon sahibi bilgileri
  - Lisans durumu
  - Üye sayısı ve istatistikleri
  - Finansal özet

• Salon Durumu Kontrolü
  - Aktif/Pasif yapma
  - Geçici durdurma
  - Lisans askıya alma
  - Durum değişiklik geçmişi

• Salon Düzenleme
  - Şirket bilgilerini güncelleme
  - İletişim bilgilerini değiştirme
  - Salon sahibi bilgilerini düzenleme
  - Adres güncelleme

• Salon Silme
  - Güvenli silme işlemi
  - Veri yedekleme
  - İlişkili verilerin kontrolü
  - Geri dönüşüm seçenekleri

2.2 HIZLI SALON EKLEME (MEVCUT):
--------------------------------
• 4 Adımlı Wizard
  - Adım 1: Kullanıcı Kaydı
  - Adım 2: Şirket Bilgileri
  - Adım 3: Adres Bilgileri
  - Adım 4: Salon Sahibi Bilgileri

• Otomatik Hesap Oluşturma
  - Admin hesabı otomatik kurulum
  - Geçici şifre oluşturma
  - Email bilgilendirme

• Lisans Atama
  - İlk kurulumda lisans paketi seçimi
  - Deneme süresi tanımlama
  - Otomatik aktivasyon

2.3 SALON GEÇİŞ SİSTEMİ:
-------------------------
• Aktif Salon Değiştirme
  - Dropdown ile salon seçimi
  - Hızlı geçiş butonları
  - Son kullanılan salonlar

• Salon Bazlı Veri İzolasyonu
  - Her salon kendi verilerine erişim
  - Cross-tenant veri koruması
  - Güvenli veri ayrımı

• Geçiş Geçmişi
  - Hangi salona ne zaman geçildi
  - Kullanıcı aktivite logları
  - Güvenlik denetimi

================================================================================
3. LİSANS YÖNETİM SİSTEMİ
================================================================================

3.1 LİSANS PAKETLERİ:
---------------------
• Paket Oluşturma/Düzenleme
  - Admin paketleri tanımlama
  - Member paketleri oluşturma
  - Özel paket seçenekleri
  - Paket özelliklerini belirleme

• Fiyatlandırma Yönetimi
  - Süre bazlı fiyatlandırma
  - İndirim kampanyaları
  - Toplu satış fiyatları
  - Dinamik fiyatlandırma

• Paket Durumu
  - Aktif/Pasif paket kontrolü
  - Paket kullanım istatistikleri
  - Popüler paket analizi

3.2 KULLANICI LİSANSLARI:
-------------------------
• Lisans Atama
  - Salon sahiplerine lisans verme
  - Toplu lisans atama
  - Otomatik lisans yenileme
  - Lisans transfer işlemleri

• Lisans Uzatma
  - Manuel süre uzatma
  - Otomatik yenileme ayarları
  - Erken yenileme indirimleri
  - Süre hesaplama algoritması

• Lisans İptali
  - Gerektiğinde iptal etme
  - Kısmi iade hesaplaması
  - İptal sebepleri kaydetme
  - Yeniden aktivasyon seçenekleri

• Lisans Geçmişi
  - Tüm işlem kayıtları
  - Ödeme geçmişi
  - Durum değişiklikleri
  - Audit trail

3.3 LİSANS İŞLEMLERİ:
---------------------
• Ödeme Takibi
  - Tüm finansal işlemler
  - Ödeme yöntemleri
  - Başarısız ödeme takibi
  - Ödeme hatırlatmaları

• Otomatik Faturalama
  - Periyodik ödemeler
  - Fatura oluşturma
  - Email ile gönderim
  - Vergi hesaplamaları

• Gelir Raporları
  - Detaylı finansal analiz
  - Aylık/yıllık raporlar
  - Karlılık analizi
  - Trend analizleri

================================================================================
4. ROL VE YETKİ YÖNETİMİ
================================================================================

4.1 SİSTEM ROLLERİ:
-------------------
• Rol Tanımlama
  - Yeni roller oluşturma
  - Rol açıklamaları
  - Rol kategorileri
  - Rol öncelikleri

• Yetki Atama
  - Rollere özel yetkiler
  - Modül bazlı yetkiler
  - CRUD işlem yetkileri
  - Özel fonksiyon yetkileri

• Rol Hiyerarşisi
  - Owner (En üst yetki)
  - Admin (Salon yönetimi)
  - Member (Temel kullanım)
  - Custom (Özel roller)

4.2 KULLANICI YETKİLERİ:
------------------------
• Kullanıcı-Rol Eşleştirme
  - Kişilere rol atama
  - Çoklu rol desteği
  - Geçici yetki verme
  - Yetki süresi belirleme

• Yetki Kontrolü
  - Hangi kullanıcı neyi görebilir
  - Sayfa bazlı erişim kontrolü
  - API endpoint yetkileri
  - Veri seviyesi güvenlik

• Toplu Yetki İşlemleri
  - Çoklu kullanıcı yönetimi
  - Grup bazlı yetki atama
  - Toplu yetki kaldırma
  - Yetki şablonları

================================================================================
5. SİSTEM YÖNETİMİ
================================================================================

5.1 TEKNİK YÖNETİM:
-------------------
• Cache Yönetimi
  - Redis cache kontrolü
  - Cache temizleme
  - Cache istatistikleri
  - Performans optimizasyonu

• Rate Limit Kontrolü
  - API çağrı limitleri
  - Güvenlik testleri
  - DDoS koruması
  - Kullanıcı bazlı limitler

• Aktif Cihaz Yönetimi
  - Oturum kontrolü
  - Cihaz listesi
  - Uzaktan oturum kapatma
  - Güvenlik uyarıları

• Sistem Logları
  - Detaylı işlem kayıtları
  - Hata logları
  - Performans logları
  - Güvenlik logları

5.2 GÜVENLİK:
-------------
• Şifre Politikaları
  - Güvenlik kuralları
  - Şifre karmaşıklığı
  - Şifre geçmişi
  - Zorunlu değişiklik

• Oturum Yönetimi
  - Aktif kullanıcı kontrolü
  - Oturum süresi
  - Çoklu oturum kontrolü
  - Güvenli çıkış

• IP Kısıtlamaları
  - Erişim kontrolü
  - Beyaz liste yönetimi
  - Kara liste kontrolü
  - Coğrafi kısıtlamalar

================================================================================
6. RAPORLAMA VE ANALİTİK
================================================================================

6.1 FİNANSAL RAPORLAR:
----------------------
• Gelir Analizi
  - Salon bazlı karlılık
  - Aylık gelir trendi
  - Yıllık karşılaştırma
  - Bütçe vs gerçekleşen

• Lisans Satış Raporları
  - Hangi paket ne kadar satıldı
  - En popüler paketler
  - Satış trendi analizi
  - Müşteri segmentasyonu

• Ödeme Takibi
  - Geciken ödemeler
  - Ödeme başarı oranları
  - Ödeme yöntemi analizi
  - Risk analizi

• Vergi Raporları
  - Muhasebe entegrasyonu
  - KDV hesaplamaları
  - Gelir vergisi raporları
  - Mali müşavir raporları

6.2 OPERASYONEL RAPORLAR:
-------------------------
• Salon Performansı
  - Üye sayısı istatistikleri
  - Aktivite raporları
  - Kapasite kullanımı
  - Verimlilik analizi

• Kullanıcı Aktivitesi
  - Giriş-çıkış istatistikleri
  - Kullanım sıklığı
  - Popüler özellikler
  - Kullanıcı memnuniyeti

• Sistem Kullanımı
  - API çağrı istatistikleri
  - Performans metrikleri
  - Hata oranları
  - Sistem yükü analizi

================================================================================
7. GELECEK GELİŞTİRMELER
================================================================================

7.1 PLANLANAN ÖZELLİKLER:
-------------------------
• Mobil Uygulama Yönetimi
  - Salon sahipleri için mobil kontrol
  - Push notification yönetimi
  - Mobil analitik
  - Offline destek

• API Yönetimi
  - Üçüncü parti entegrasyonlar
  - API key yönetimi
  - Rate limiting
  - API dokümantasyonu

• Bildirim Sistemi
  - Email otomasyonu
  - SMS bildirimleri
  - In-app notifications
  - Bildirim şablonları

• Backup Yönetimi
  - Otomatik yedekleme
  - Veri güvenliği
  - Disaster recovery
  - Cloud backup entegrasyonu

================================================================================
8. ÖNCELİKLİ GELİŞTİRME ÖNERİLERİ
================================================================================

PHASE 1 (Yüksek Öncelik):
-------------------------
1. Ana Dashboard Oluşturma
   - Owner için merkezi kontrol paneli
   - Temel istatistik kartları
   - Grafik entegrasyonu

2. Salon Listesi Sayfası
   - Tüm salonları görüntüleme
   - Filtreleme ve arama
   - Temel düzenleme işlemleri

PHASE 2 (Orta Öncelik):
-----------------------
3. Gelişmiş Lisans Yönetimi
   - Mevcut sistemi güçlendirme
   - Otomatik yenileme
   - Gelişmiş raporlama

4. Finansal Raporlama
   - Gelir-gider takibi
   - Karlılık analizi
   - Vergi raporları

PHASE 3 (Düşük Öncelik):
------------------------
5. Sistem Monitoring
   - Performans izleme
   - Güvenlik monitoring
   - Log analizi

6. Gelişmiş Analitik
   - Predictive analytics
   - Machine learning entegrasyonu
   - Trend analizi

================================================================================
9. MEVCUT DURUM ANALİZİ
================================================================================

GÜÇLÜ YANLAR:
-------------
✅ Lisans yönetim sistemi mevcut ve çalışıyor
✅ Multi-tenant mimari hazır ve aktif
✅ Rol tabanlı yetki sistemi çalışıyor
✅ Salon hızlı ekleme wizard'ı var ve fonksiyonel
✅ Company context sistemi aktif
✅ Cache yönetimi mevcut
✅ Rate limiting sistemi var
✅ Güvenlik altyapısı sağlam

GELİŞTİRİLMESİ GEREKEN ALANLAR:
-------------------------------
🔄 Owner dashboard'u eksik
🔄 Salon listesi ve detay sayfaları yok
🔄 Finansal raporlama sistemi eksik
🔄 Sistem monitoring araçları yetersiz
🔄 Gelişmiş analitik raporlar yok
🔄 Bildirim sistemi eksik
🔄 API yönetimi geliştirilmeli

================================================================================
10. SONUÇ VE ÖNERİLER
================================================================================

Bu plan doğrultusunda sistemin owner yetkisi ile tam bir spor salonu yönetim 
merkezi haline gelmesi hedeflenmektedir. Mevcut güçlü altyapı üzerine inşa 
edilecek bu özellikler ile:

• Merkezi yönetim kolaylığı sağlanacak
• Finansal kontrol artırılacak
• Operasyonel verimlilik yükselecek
• Güvenlik ve performans optimize edilecek
• Gelecek genişlemeler için sağlam temel oluşacak

Önerilen geliştirme sırası Phase 1'den başlayarak adım adım ilerlemektir.
Her phase tamamlandıktan sonra kullanıcı geri bildirimleri alınarak 
bir sonraki phase'e geçilmelidir.

================================================================================
DOKÜMAN SONU
================================================================================
